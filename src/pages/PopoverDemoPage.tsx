import { useState } from "react";
import { Calendar, Code, ExternalLink, Settings } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { BookingPlugin, FloatingBookingButton, InlineBookingWidget } from "@/components/booking/BookingPlugin";
import { useCompanyConfig } from "@/config/company";

const PopoverDemoPage = () => {
  const companyConfig = useCompanyConfig();
  const [activeDemo, setActiveDemo] = useState<'floating' | 'inline' | 'trigger'>('floating');

  const handleBookingComplete = (data: any) => {
    console.log('Booking completed:', data);
  };

  const handleBookingStart = () => {
    console.log('Booking started');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="text-center mb-12">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full mb-6">
            <Calendar className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent mb-4">
            Booking Plugin Demo
          </h1>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Experience our booking system as a plugin that can be embedded on any website. 
            Choose from different integration modes below.
          </p>
        </div>

        {/* Navigation */}
        <div className="flex justify-center mb-8">
          <div className="flex gap-2 p-1 bg-muted rounded-lg">
            <Button
              variant={activeDemo === 'floating' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveDemo('floating')}
            >
              Floating Button
            </Button>
            <Button
              variant={activeDemo === 'inline' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveDemo('inline')}
            >
              Inline Widget
            </Button>
            <Button
              variant={activeDemo === 'trigger' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setActiveDemo('trigger')}
            >
              Custom Trigger
            </Button>
          </div>
        </div>

        {/* Demo Content */}
        <div className="max-w-4xl mx-auto">
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            {/* Demo Description */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="w-5 h-5" />
                  {activeDemo === 'floating' && 'Floating Action Button'}
                  {activeDemo === 'inline' && 'Inline Booking Widget'}
                  {activeDemo === 'trigger' && 'Custom Trigger Button'}
                </CardTitle>
                <CardDescription>
                  {activeDemo === 'floating' && 'A floating button that stays fixed on the page, perfect for always-available booking access.'}
                  {activeDemo === 'inline' && 'An inline widget that can be embedded directly in your content flow.'}
                  {activeDemo === 'trigger' && 'A customizable trigger button that can be positioned anywhere on your page.'}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h4 className="font-semibold mb-2">Features:</h4>
                    <ul className="space-y-1 text-sm text-muted-foreground">
                      <li>• Clean progress bar (no dots)</li>
                      <li>• Company branding integration</li>
                      <li>• Mobile-responsive design</li>
                      <li>• Easy customization</li>
                      <li>• Multiple trigger options</li>
                    </ul>
                  </div>
                  
                  <div>
                    <h4 className="font-semibold mb-2">Integration:</h4>
                    <Badge variant="secondary" className="text-xs">
                      <Code className="w-3 h-3 mr-1" />
                      Plugin Ready
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Sample Website Content */}
            <Card>
              <CardHeader>
                <CardTitle>Sample Website Content</CardTitle>
                <CardDescription>
                  This represents your website content where the booking plugin would be embedded.
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-lg mb-2">{companyConfig.name} Services</h3>
                    <p className="text-muted-foreground text-sm mb-4">
                      We provide professional home services including plumbing, electrical work, 
                      and HVAC maintenance. Our certified technicians are available 24/7 to help 
                      with all your home service needs.
                    </p>
                  </div>

                  {activeDemo === 'inline' && (
                    <InlineBookingWidget
                      onBookingComplete={handleBookingComplete}
                      onBookingStart={handleBookingStart}
                    />
                  )}

                  <div className="text-sm text-muted-foreground">
                    <p>
                      Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod 
                      tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, 
                      quis nostrud exercitation ullamco laboris.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Links */}
          <div className="text-center space-y-4">
            <h3 className="text-xl font-semibold">Try Different Versions</h3>
            <div className="flex flex-wrap justify-center gap-4">
              <Button variant="outline" asChild>
                <a href="/booking">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Full Page Version
                </a>
              </Button>
              <Button variant="outline" asChild>
                <a href="/">
                  <ExternalLink className="w-4 h-4 mr-2" />
                  Original Version
                </a>
              </Button>
            </div>
          </div>
        </div>

        {/* Plugin Components */}
        {activeDemo === 'floating' && (
          <FloatingBookingButton
            onBookingComplete={handleBookingComplete}
            onBookingStart={handleBookingStart}
          />
        )}

        {activeDemo === 'trigger' && (
          <BookingPlugin
            config={{
              popover: {
                width: 480,
                height: 600,
                position: 'bottom-right',
                trigger: 'button',
                triggerText: 'Schedule Service',
              }
            }}
            onBookingComplete={handleBookingComplete}
            onBookingStart={handleBookingStart}
          />
        )}
      </div>
    </div>
  );
};

export default PopoverDemoPage;
