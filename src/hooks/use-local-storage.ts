import { useState, useEffect, useCallback } from 'react';

export function useLocalStorage<T>(key: string, initialValue: T) {
  // Get from local storage then parse stored json or return initialValue
  const [storedValue, setStoredValue] = useState<T>(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key "${key}":`, error);
      return initialValue;
    }
  });

  // Return a wrapped version of useState's setter function that persists the new value to localStorage
  const setValue = (value: T | ((val: T) => T)) => {
    try {
      // Allow value to be a function so we have the same API as useState
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      
      // Save state
      setStoredValue(valueToStore);
      
      // Save to local storage
      if (valueToStore === undefined || valueToStore === null) {
        window.localStorage.removeItem(key);
      } else {
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      }
    } catch (error) {
      console.warn(`Error setting localStorage key "${key}":`, error);
    }
  };

  // Clear the stored value
  const clearValue = () => {
    try {
      window.localStorage.removeItem(key);
      setStoredValue(initialValue);
    } catch (error) {
      console.warn(`Error clearing localStorage key "${key}":`, error);
    }
  };

  return [storedValue, setValue, clearValue] as const;
}

// Specific hook for booking form data
export interface BookingFormData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: string; // Store as ISO string for localStorage compatibility
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  address?: string;
  notes?: string;
  currentStep?: number;
}

export function useBookingFormStorage() {
  const [bookingData, setBookingData, clearBookingData] = useLocalStorage<BookingFormData>(
    'booking-form-data',
    {}
  );

  // Helper to update specific fields
  const updateBookingData = useCallback((updates: Partial<BookingFormData>) => {
    setBookingData(prev => ({ ...prev, ...updates }));
  }, [setBookingData]);

  // Helper to get date as Date object
  const getDateAsObject = useCallback((): Date | undefined => {
    if (bookingData.date) {
      try {
        return new Date(bookingData.date);
      } catch {
        return undefined;
      }
    }
    return undefined;
  }, [bookingData.date]);

  // Helper to set date from Date object
  const setDateFromObject = useCallback((date: Date | undefined) => {
    updateBookingData({ date: date?.toISOString() });
  }, [updateBookingData]);

  return {
    bookingData,
    setBookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  };
}
