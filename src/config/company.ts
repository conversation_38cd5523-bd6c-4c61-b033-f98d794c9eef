export interface CompanyConfig {
  name: string;
  logo?: string;
  tagline?: string;
  colors: {
    primary: string;
    secondary: string;
    accent: string;
  };
  contact: {
    phone?: string;
    email?: string;
    website?: string;
  };
  services: string[];
}

// Default company configuration - can be easily customized for different clients
export const defaultCompanyConfig: CompanyConfig = {
  name: "ServicePro",
  tagline: "Make it a great day!",
  logo: "/logo.svg", // Path to company logo
  colors: {
    primary: "hsl(259 94% 51%)", // Purple from existing theme
    secondary: "hsl(142 71% 45%)", // Green from existing theme
    accent: "hsl(259 100% 97%)", // Light purple from existing theme
  },
  contact: {
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://servicepro.com",
  },
  services: [
    "Plumbing",
    "Heating & Cooling", 
    "Electrical",
    "General Maintenance"
  ]
};

// Plugin configuration for easy customization
export interface PluginConfig extends CompanyConfig {
  mode: 'fullpage' | 'popover';
  popover?: {
    width: number;
    height: number;
    position: 'center' | 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    trigger?: 'button' | 'auto' | 'scroll';
    triggerText?: string;
  };
  theme: 'light' | 'dark' | 'auto';
}

export const defaultPluginConfig: PluginConfig = {
  ...defaultCompanyConfig,
  mode: 'popover',
  popover: {
    width: 480,
    height: 600,
    position: 'bottom-right',
    trigger: 'button',
    triggerText: 'Book Service',
  },
  theme: 'auto',
};

// Hook to use company configuration
export const useCompanyConfig = () => {
  // In a real implementation, this could fetch from API or local storage
  // For now, return the default configuration
  return defaultCompanyConfig;
};

// Hook to use plugin configuration
export const usePluginConfig = () => {
  // In a real implementation, this could be customized per client
  return defaultPluginConfig;
};
