import { useState } from "react";
import {
  MapPinned,
  Home,
  Pen,
  User,
  Mail,
  Phone,
  CheckCircle,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

import { cn } from "@/lib/utils";

interface LocationData {
  houseNumber: string;
  street: string;
  city: string;
}

interface ContactInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
}

interface LocationStepProps {
  onNext: (data: LocationData, zipCode: string) => void;
  onBack: () => void;
  zipCode: string;
  initialData?: Partial<LocationData>;
  contactInfo?: ContactInfo;
  onEditContact?: () => void;
}

export function LocationStep({
  onNext,
  onBack,
  zipCode,
  initialData,
  contactInfo,
  onEditContact,
}: LocationStepProps) {
  const [formData, setFormData] = useState<LocationData>({
    houseNumber: initialData?.houseNumber || "",
    street: initialData?.street || "",
    city: initialData?.city || "",
  });
  const [errors, setErrors] = useState<Partial<LocationData>>({});

  const validateForm = () => {
    const newErrors: Partial<LocationData> = {};

    if (!formData.houseNumber.trim()) {
      newErrors.houseNumber = "Please enter your house number";
    }
    if (!formData.street.trim()) {
      newErrors.street = "Please enter your street address";
    }
    if (!formData.city.trim()) {
      newErrors.city = "Please enter your city";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      onNext(formData, zipCode);
    }
  };

  const updateField = (field: keyof LocationData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    if (errors[field]) {
      setErrors((prev) => ({ ...prev, [field]: undefined }));
    }
  };
  return (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <div className="w-16 h-16 bg-accent rounded-full flex items-center justify-center mx-auto mb-4">
          <Home className="w-8 h-8 text-primary" />
        </div>
        <h2 className="text-2xl font-bold text-foreground mb-3">
          Where do you need service?
        </h2>
        <p className="text-muted-foreground">
          Please provide your service address and verify your contact
          information.
        </p>
      </div>

      {/* Contact Info Summary (Editable) */}
      {contactInfo && (
        <Card className="mb-6 border-l-4 border-l-primary">
          <CardHeader className="pb-1">
            <CardTitle className="text-lg flex items-center justify-between">
              <span className="flex items-center gap-2">
                <User className="w-5 h-5 text-primary" />
                Contact Information
              </span>
              {onEditContact && (
                <Button variant="ghost" size="sm" onClick={onEditContact}>
                  <Pen className="w-4 h-4" />
                </Button>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-0 text-sm py-1">
            <div className="font-medium">
              {contactInfo.firstName} {contactInfo.lastName}
            </div>
            <div className="text-muted-foreground">{contactInfo.email}</div>
            <div className="text-muted-foreground">{contactInfo.phone}</div>
          </CardContent>
        </Card>
      )}

      <form onSubmit={handleSubmit} className="space-y-6">
        <div>
          <div className="grid grid-cols-[1fr_auto] gap-2">
            <div className="flex flex-col gap-1">
              <Label htmlFor="street">
                Street Address <span className="text-destructive">*</span>
              </Label>
              <Input
                id="street"
                value={formData.street}
                onChange={(e) => updateField("street", e.target.value)}
                className={errors.street ? "border-destructive" : ""}
                placeholder="Enter street address"
              />
            </div>
            <div className="flex flex-col gap-1">
              <Label htmlFor="houseNumber">
                House No. <span className="text-destructive">*</span>
              </Label>
              <Input
                id="houseNumber"
                type="number"
                value={formData.houseNumber}
                onChange={(e) => updateField("houseNumber", e.target.value)}
                className={cn(
                  "w-24",
                  errors.houseNumber ? "border-destructive" : ""
                )}
                placeholder="No."
              />
            </div>
          </div>
          {errors.houseNumber && (
            <p className="text-destructive text-sm mt-1">
              {errors.houseNumber}
            </p>
          )}
          {errors.street && (
            <p className="text-destructive text-sm mt-1">{errors.street}</p>
          )}
        </div>

        <div>
          <Label htmlFor="city">
            City <span className="text-destructive">*</span>
          </Label>
          <Input
            id="city"
            value={formData.city}
            onChange={(e) => updateField("city", e.target.value)}
            className={errors.city ? "border-destructive" : ""}
            placeholder="Enter city"
          />
          {errors.city && (
            <p className="text-destructive text-sm mt-1">{errors.city}</p>
          )}
        </div>

        <div>
          <Label htmlFor="zipCode">
            Zip Code <span className="text-destructive">*</span>
          </Label>
          <Input
            id="zipCode"
            type="text"
            value={zipCode}
            readOnly
            className="bg-muted cursor-not-allowed"
          />
        </div>

        <div className="flex gap-4 pt-4">
          <Button variant="outline" onClick={onBack} className="flex-1">
            Back
          </Button>
          <Button
            type="submit"
            className="flex-1 bg-gradient-primary hover:opacity-90 transition-opacity"
          >
            Continue
          </Button>
        </div>
      </form>
    </div>
  );
}
