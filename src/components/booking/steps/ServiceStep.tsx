import { useState } from "react";
import { <PERSON><PERSON>, User, Home, Pen } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
  PlumbingIcon,
  ElectricalIcon,
  HVACIcon,
} from "@/components/icons/ServiceIcons";

interface ServiceStepProps {
  onNext: (data: { service: string; description: string }) => void;
  onBack: () => void;
  initialData?: { service: string; description: string };
  contactInfo?: {
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
  };
  locationInfo?: {
    zipCode: string;
    houseNumber: string;
    street: string;
    city: string;
  };
  onEditContact?: () => void;
  onEditLocation?: () => void;
}

const services = {
  Plumbing: {
    Repair: [
      "Find & repair leak",
      "Repair faucet",
      "Repair garbage disposal",
      "Repair outdoor systems",
      "Repair pipe",
      "Repair sewer",
      "Repair shower",
      "Repair toilet",
      "Repair water heater",
      "Unclog drain",
    ],
    Install: [
      "Install faucet",
      "Install garbage disposal",
      "Install shower",
      "Install toilet",
      "Install water heater",
    ],
    Other: [],
  },
  "Heating & Cooling": {
    Repair: [
      "Ductless heating and AC services",
      "Repair AC",
      "Repair HVAC",
      "Repair ducts & vents",
      "Repair heating system",
      "Repair thermostat",
    ],
    Install: [
      "Install AC",
      "Install ducts & vents",
      "Install heating system",
      "Install thermostat",
    ],
    Maintenance: ["AC maintenance", "HVAC maintenance", "Heating maintenance"],
    Other: [],
  },
  Electrical: {
    Repair: [
      "Repair fan",
      "Repair light fixtures",
      "Repair outlets or switches",
      "Repair panel",
      "Restore power",
    ],
    Install: [
      "Install electric car charger",
      "Install fan",
      "Install ground wire",
      "Install light fixtures",
      "Install outdoor lighting",
      "Install outlets or switches",
      "Install security system",
      "Relocate outlets or switches",
      "Remodeling",
      "Replace or upgrade panel",
    ],
    Other: [],
  },
};

const serviceIcons = {
  Plumbing: PlumbingIcon,
  "Heating & Cooling": HVACIcon,
  Electrical: ElectricalIcon,
};

const serviceColors = {
  Plumbing: "text-blue-600",
  "Heating & Cooling": "text-green-600",
  Electrical: "text-yellow-600",
};

export function ServiceStep({
  onNext,
  onBack,
  initialData,
  contactInfo,
  locationInfo,
  onEditContact,
  onEditLocation,
}: ServiceStepProps) {
  const [selectedService, setSelectedService] = useState(
    initialData?.service || ""
  );
  const [selectedSubService, setSelectedSubService] = useState("");
  const [selectedSpecificService, setSelectedSpecificService] = useState("");
  const [description, setDescription] = useState(
    initialData?.description || ""
  );

  const handleServiceSelect = (serviceName: string) => {
    setSelectedService(serviceName);
    setSelectedSubService("");
    setSelectedSpecificService("");
  };

  const handleSubServiceSelect = (subServiceType: string) => {
    setSelectedSubService(subServiceType);
    setSelectedSpecificService("");
  };

  const handleNext = () => {
    if (!selectedService) return;

    let finalService = selectedService;
    if (selectedSubService && selectedSpecificService) {
      finalService = `${selectedService} - ${selectedSubService} - ${selectedSpecificService}`;
    } else if (selectedSubService) {
      finalService = `${selectedService} - ${selectedSubService}`;
    }

    onNext({
      service: finalService,
      description: description.trim(),
    });
  };

  const canContinue = () => {
    if (!selectedService) return false;
    const subServices = services[selectedService as keyof typeof services];
    if (!subServices) return false;
    if (Object.keys(subServices).length > 0 && !selectedSubService)
      return false;

    const specificOptions =
      subServices[selectedSubService as keyof typeof subServices];
    if (
      Array.isArray(specificOptions) &&
      specificOptions.length > 0 &&
      !selectedSpecificService
    )
      return false;

    return true;
  };

  return (
    <div className="max-w-2xl mx-auto">
      {/* Contact and Location Summary */}
      <div className="grid md:grid-cols-2 gap-4 mb-8">
        {contactInfo && (
          <Card className="border-l-4 border-l-primary">
            <CardHeader className="pb-1">
              <CardTitle className="text-sm flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <User className="w-4 h-4 text-primary" />
                  Contact Information
                </span>
                {onEditContact && (
                  <Button variant="ghost" size="sm" onClick={onEditContact}>
                    <Pen className="w-4 h-4" />
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-0 text-sm py-1">
              <div className="font-medium">
                {contactInfo.firstName} {contactInfo.lastName}
              </div>
              <div className="text-muted-foreground">{contactInfo.email}</div>
              <div className="text-muted-foreground">{contactInfo.phone}</div>
            </CardContent>
          </Card>
        )}

        {locationInfo && (
          <Card className="border-l-4 border-l-primary">
            <CardHeader className="">
              <CardTitle className="text-sm flex items-center justify-between">
                <span className="flex items-center gap-2">
                  <Home className="w-4 h-4 text-primary" />
                  Service Location
                </span>
                {onEditLocation && (
                  <Button variant="ghost" size="sm" onClick={onEditLocation}>
                    <Pen className="w-4 h-4" />
                  </Button>
                )}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-0 text-sm py-1">
              <div className="font-medium">
                {locationInfo.houseNumber} {locationInfo.street}
              </div>
              <div className="text-muted-foreground">
                {locationInfo.city}, {locationInfo.zipCode}
              </div>
            </CardContent>
          </Card>
        )}
      </div>

      <div className="text-center mb-8">
        <h2 className="text-2xl font-bold text-foreground mb-3">
          How can we improve your comfort today?
        </h2>
        <p className="text-muted-foreground">
          Select the type of service you need assistance with.
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-8">
        {Object.keys(services).map((serviceName) => {
          const Icon = serviceIcons[serviceName as keyof typeof serviceIcons];
          const isSelected = selectedService === serviceName;

          return (
            <Card
              key={serviceName}
              className={cn(
                "cursor-pointer transition-all duration-200 hover:shadow-md border-2",
                isSelected
                  ? "border-primary bg-accent"
                  : "border-border hover:border-primary/30"
              )}
              onClick={() => handleServiceSelect(serviceName)}
            >
              <CardContent className="p-6 text-center">
                <div
                  className={cn(
                    "p-4 rounded-xl bg-gradient-to-br from-white to-gray-50 dark:from-gray-700 dark:to-gray-800 mx-auto mb-4 w-fit shadow-lg",
                    serviceColors[serviceName as keyof typeof serviceColors]
                  )}
                >
                  <Icon className="w-8 h-8" />
                </div>
                <h3 className="font-semibold text-foreground mb-2 text-lg">
                  {serviceName}
                </h3>
                <p className="text-sm text-muted-foreground">
                  Professional {serviceName.toLowerCase()} services
                </p>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {selectedService &&
        services[selectedService as keyof typeof services] && // Added check here
        Object.keys(services[selectedService as keyof typeof services]).length >
          0 && (
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-foreground mb-4">
              Please select your service type
            </h3>
            <div className="flex gap-2 mb-6">
              {Object.keys(
                services[selectedService as keyof typeof services]
              ).map((subServiceType) => (
                <Button
                  key={subServiceType}
                  variant={
                    selectedSubService === subServiceType
                      ? "default"
                      : "outline"
                  }
                  onClick={() => handleSubServiceSelect(subServiceType)}
                >
                  {subServiceType}
                </Button>
              ))}
            </div>

            {selectedSubService &&
              selectedSubService !== "Other" &&
              Array.isArray(
                services[selectedService as keyof typeof services][
                  selectedSubService as keyof (typeof services)[keyof typeof services]
                ]
              ) && (
                <div>
                  <h4 className="text-md font-semibold text-foreground mb-4">
                    What needs to be {selectedSubService.toLowerCase()}?
                  </h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {(
                      services[selectedService as keyof typeof services][
                        selectedSubService as keyof (typeof services)[keyof typeof services]
                      ] as string[]
                    ).map((option) => (
                      <Button
                        key={option}
                        variant={
                          selectedSpecificService === option
                            ? "default"
                            : "outline"
                        }
                        onClick={() => setSelectedSpecificService(option)}
                        size="sm"
                        className="text-xs py-2 px-3"
                      >
                        {option}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
          </div>
        )}

      <div className="mb-8">
        <label className="block text-sm font-medium text-foreground mb-2">
          Describe your issue (optional)
        </label>
        <textarea
          value={description}
          onChange={(e) => setDescription(e.target.value)}
          placeholder="Tell us more about what you need help with..."
          className="w-full min-h-24 p-3 border border-input rounded-lg bg-background text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring"
          rows={3}
        />
      </div>

      <div className="flex gap-4">
        <Button variant="outline" onClick={onBack} className="flex-1">
          Back
        </Button>
        <Button
          onClick={handleNext}
          disabled={!canContinue()}
          className="flex-1 bg-gradient-primary hover:opacity-90 transition-opacity"
        >
          Continue Booking
        </Button>
      </div>
    </div>
  );
}
