import { useState } from "react";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, Shield, Clock } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useCompanyConfig } from "@/config/company";
import { cn } from "@/lib/utils";

interface WelcomeStepProps {
  onNext: () => void;
  mode?: 'fullpage' | 'popover';
}

export function WelcomeStep({ onNext, mode = 'fullpage' }: WelcomeStepProps) {
  const companyConfig = useCompanyConfig();
  const [isLoading, setIsLoading] = useState(false);

  const handleGetStarted = async () => {
    setIsLoading(true);
    // Small delay for better UX
    await new Promise(resolve => setTimeout(resolve, 300));
    onNext();
    setIsLoading(false);
  };

  const features = [
    {
      icon: Star,
      title: "Expert Service",
      description: "Qualified technicians with years of experience"
    },
    {
      icon: Shield,
      title: "Fully Insured",
      description: "Licensed and insured for your peace of mind"
    },
    {
      icon: Clock,
      title: "On-Time Service",
      description: "We respect your time and arrive when scheduled"
    }
  ];

  return (
    <div className={cn(
      "text-center",
      mode === 'popover' ? "max-w-sm mx-auto" : "max-w-lg mx-auto"
    )}>
      {/* Company Logo and Branding */}
      <div className="mb-6">
        {companyConfig.logo ? (
          <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-primary to-primary-light rounded-2xl flex items-center justify-center shadow-lg">
            <img 
              src={companyConfig.logo} 
              alt={`${companyConfig.name} Logo`}
              className="w-12 h-12 object-contain"
              onError={(e) => {
                // Fallback to icon if logo fails to load
                e.currentTarget.style.display = 'none';
                e.currentTarget.nextElementSibling?.classList.remove('hidden');
              }}
            />
            <Wrench className="w-12 h-12 text-white hidden" />
          </div>
        ) : (
          <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-primary to-primary-light rounded-2xl flex items-center justify-center shadow-lg">
            <Wrench className="w-12 h-12 text-white" />
          </div>
        )}

        <h1 className={cn(
          "font-bold bg-gradient-to-r from-primary to-primary-light bg-clip-text text-transparent mb-2",
          mode === 'popover' ? "text-2xl" : "text-3xl sm:text-4xl"
        )}>
          {companyConfig.name}
        </h1>
        
        {companyConfig.tagline && (
          <p className={cn(
            "text-muted-foreground font-medium",
            mode === 'popover' ? "text-sm" : "text-lg"
          )}>
            {companyConfig.tagline}
          </p>
        )}
      </div>

      {/* Features (only show in fullpage mode) */}
      {mode === 'fullpage' && (
        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-8">
          {features.map((feature, index) => (
            <div key={index} className="text-center p-4">
              <div className="w-12 h-12 bg-accent rounded-full flex items-center justify-center mx-auto mb-3">
                <feature.icon className="w-6 h-6 text-primary" />
              </div>
              <h3 className="font-semibold text-foreground mb-1 text-sm">
                {feature.title}
              </h3>
              <p className="text-xs text-muted-foreground">
                {feature.description}
              </p>
            </div>
          ))}
        </div>
      )}

      {/* Call to Action */}
      <div className="space-y-4">
        <div className="text-center">
          <h2 className={cn(
            "font-bold text-foreground mb-2",
            mode === 'popover' ? "text-lg" : "text-xl"
          )}>
            Book Your Service Appointment
          </h2>
          <p className={cn(
            "text-muted-foreground",
            mode === 'popover' ? "text-sm" : "text-base"
          )}>
            {mode === 'popover' 
              ? "Quick and easy scheduling in just a few steps"
              : "Schedule your appointment in just a few easy steps. Our qualified technicians are ready to help with all your home service needs."
            }
          </p>
        </div>

        <Button
          onClick={handleGetStarted}
          disabled={isLoading}
          className={cn(
            "bg-gradient-primary hover:opacity-90 transition-opacity group",
            mode === 'popover' ? "w-full" : "w-full sm:w-auto px-8"
          )}
          size={mode === 'popover' ? "default" : "lg"}
        >
          {isLoading ? (
            "Getting Started..."
          ) : (
            <>
              Get Started
              <ArrowRight className="w-4 h-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </>
          )}
        </Button>

        {/* Contact info (only in fullpage mode) */}
        {mode === 'fullpage' && companyConfig.contact.phone && (
          <p className="text-sm text-muted-foreground mt-4">
            Need help? Call us at{" "}
            <a 
              href={`tel:${companyConfig.contact.phone}`}
              className="text-primary hover:underline font-medium"
            >
              {companyConfig.contact.phone}
            </a>
          </p>
        )}
      </div>
    </div>
  );
}
