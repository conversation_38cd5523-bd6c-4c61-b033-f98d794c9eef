import { useState, useEffect } from "react";
import { CleanProgressBar } from "./CleanProgressBar";
import { WelcomeStep } from "./steps/WelcomeStep";
import { ZipCodeStep } from "./steps/ZipCodeStep";
import { LocationStep } from "./steps/LocationStep";
import { ContactInfoStep } from "./steps/ContactInfoStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { useCompanyConfig } from "@/config/company";
import { toast } from "@/hooks/use-toast";
import { CheckCircle } from "lucide-react";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  notes?: string;
}

const STEPS = [
  "Welcome",
  "Location", 
  "Contact",
  "Address",
  "Service",
  "Schedule",
  "Confirm",
];

export function FullPageBooking() {
  const companyConfig = useCompanyConfig();
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState(bookingData.currentStep || 1);
  const [isComplete, setIsComplete] = useState(false);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
    }

    const nextStep = currentStep + 1;
    updateBookingData({ ...dataToStore, currentStep: nextStep });
    if (currentStep < STEPS.length) {
      setCurrentStep(nextStep);
    }
  };

  const handleLocationStepNext = (
    data: { street: string; city: string; state: string },
    zipCode: string
  ) => {
    goToNextStep({ ...data, zipCode });
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      updateBookingData({ currentStep: newStep });
    }
  };

  const handleEditStep = (step: string) => {
    switch (step) {
      case "start":
        setCurrentStep(1);
        break;
      case "service":
        setCurrentStep(5);
        break;
      case "schedule":
        setCurrentStep(6);
        break;
      case "contact":
        setCurrentStep(3);
        break;
      case "location":
        setCurrentStep(4);
        break;
      default:
        break;
    }
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    toast({
      title: "Booking Confirmed!",
      description: "We'll be in touch shortly to confirm your appointment details.",
    });
  };

  const handleStartNewBooking = () => {
    clearBookingData();
    setCurrentStep(1);
    setIsComplete(false);
  };

  if (isComplete) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="w-20 h-20 bg-booking-step-complete rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Booking Confirmed!
          </h1>
          <p className="text-muted-foreground mb-8">
            Thank you for choosing {companyConfig.name}. We'll be in touch shortly to
            confirm your appointment details.
          </p>
          <button
            onClick={handleStartNewBooking}
            className="w-full bg-gradient-primary text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity"
          >
            Book Another Appointment
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-6 overflow-y-auto">
        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto mb-8">
          <CleanProgressBar
            currentStep={currentStep}
            totalSteps={STEPS.length}
            steps={STEPS}
            showStepNames={true}
          />
        </div>

        {/* Form Steps */}
        <div className="max-w-4xl mx-auto bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-6">
          {/* Step 1: Welcome */}
          {currentStep === 1 && (
            <WelcomeStep onNext={() => goToNextStep({})} mode="fullpage" />
          )}

          {/* Step 2: Zip Code */}
          {currentStep === 2 && (
            <ZipCodeStep
              onNext={goToNextStep}
              initialData={{ zipCode: bookingData.zipCode || "" }}
            />
          )}

          {/* Step 3: Contact Info */}
          {currentStep === 3 && (
            <ContactInfoStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                firstName: bookingData.firstName,
                lastName: bookingData.lastName,
                email: bookingData.email,
                phone: bookingData.phone,
              }}
            />
          )}

          {/* Step 4: Location */}
          {currentStep === 4 && bookingData.zipCode && (
            <LocationStep
              onNext={handleLocationStepNext}
              onBack={goToPreviousStep}
              zipCode={bookingData.zipCode}
              initialData={{
                houseNumber: bookingData.houseNumber || "",
                street: bookingData.street || "",
                city: bookingData.city || "",
              }}
            />
          )}

          {/* Step 5: Service */}
          {currentStep === 5 && (
            <ServiceStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                service: bookingData.service || "",
                description: bookingData.description || "",
              }}
            />
          )}

          {/* Step 6: Schedule */}
          {currentStep === 6 && (
            <ScheduleStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                date: getDateAsObject(),
                time: bookingData.time || "",
              }}
            />
          )}

          {/* Step 7: Confirmation */}
          {currentStep === 7 &&
            bookingData.zipCode &&
            bookingData.service &&
            getDateAsObject() &&
            bookingData.time && (
              <ConfirmationStep
                data={{
                  ...bookingData,
                  date: getDateAsObject() || new Date(),
                } as any}
                onBack={goToPreviousStep}
                onConfirm={handleConfirmBooking}
                onEdit={handleEditStep}
              />
            )}
        </div>
      </div>
    </div>
  );
}
