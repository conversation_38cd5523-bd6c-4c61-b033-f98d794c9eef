import { useState, useEffect } from "react";
import { Calendar, X } from "lucide-react";
import { Button } from "@/components/ui/button";
import { PopoverBooking } from "./PopoverBooking";
import { PluginConfig, usePluginConfig } from "@/config/company";
import { cn } from "@/lib/utils";

interface BookingPluginProps {
  config?: Partial<PluginConfig>;
  onBookingComplete?: (bookingData: any) => void;
  onBookingStart?: () => void;
  className?: string;
}

export function BookingPlugin({ 
  config, 
  onBookingComplete, 
  onBookingStart,
  className 
}: BookingPluginProps) {
  const defaultConfig = usePluginConfig();
  const pluginConfig = { ...defaultConfig, ...config };
  const [isOpen, setIsOpen] = useState(false);
  const [hasTriggered, setHasTriggered] = useState(false);

  // Auto-trigger logic
  useEffect(() => {
    if (pluginConfig.popover?.trigger === 'auto' && !hasTriggered) {
      const timer = setTimeout(() => {
        setIsOpen(true);
        setHasTriggered(true);
        onBookingStart?.();
      }, 3000); // Auto-open after 3 seconds

      return () => clearTimeout(timer);
    }
  }, [pluginConfig.popover?.trigger, hasTriggered, onBookingStart]);

  // Scroll trigger logic
  useEffect(() => {
    if (pluginConfig.popover?.trigger === 'scroll' && !hasTriggered) {
      const handleScroll = () => {
        const scrollPercent = (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100;
        if (scrollPercent > 50) { // Trigger when 50% scrolled
          setIsOpen(true);
          setHasTriggered(true);
          onBookingStart?.();
          window.removeEventListener('scroll', handleScroll);
        }
      };

      window.addEventListener('scroll', handleScroll);
      return () => window.removeEventListener('scroll', handleScroll);
    }
  }, [pluginConfig.popover?.trigger, hasTriggered, onBookingStart]);

  const handleOpenBooking = () => {
    setIsOpen(true);
    onBookingStart?.();
  };

  const handleCloseBooking = () => {
    setIsOpen(false);
  };

  const handleBookingComplete = (data: any) => {
    onBookingComplete?.(data);
    setIsOpen(false);
  };

  const getPositionClasses = () => {
    const position = pluginConfig.popover?.position || 'bottom-right';
    
    switch (position) {
      case 'bottom-right':
        return 'fixed bottom-6 right-6';
      case 'bottom-left':
        return 'fixed bottom-6 left-6';
      case 'top-right':
        return 'fixed top-6 right-6';
      case 'top-left':
        return 'fixed top-6 left-6';
      case 'center':
        return 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2';
      default:
        return 'fixed bottom-6 right-6';
    }
  };

  // Only show trigger button for button trigger mode
  const showTriggerButton = pluginConfig.popover?.trigger === 'button' || !pluginConfig.popover?.trigger;

  return (
    <>
      {/* Trigger Button */}
      {showTriggerButton && (
        <div className={cn(getPositionClasses(), "z-40", className)}>
          <Button
            onClick={handleOpenBooking}
            className="bg-gradient-primary hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl group"
            size="lg"
          >
            <Calendar className="w-5 h-5 mr-2 group-hover:scale-110 transition-transform" />
            {pluginConfig.popover?.triggerText || 'Book Service'}
          </Button>
        </div>
      )}

      {/* Popover Booking Form */}
      <PopoverBooking
        isOpen={isOpen}
        onClose={handleCloseBooking}
        onComplete={handleBookingComplete}
      />
    </>
  );
}

// Floating Action Button variant
export function FloatingBookingButton({ 
  config, 
  onBookingComplete, 
  onBookingStart,
  className 
}: BookingPluginProps) {
  const [isOpen, setIsOpen] = useState(false);

  return (
    <>
      <div className={cn("fixed bottom-6 right-6 z-40", className)}>
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-14 h-14 rounded-full bg-gradient-primary hover:opacity-90 transition-all duration-300 shadow-lg hover:shadow-xl hover:scale-110 group p-0"
        >
          <Calendar className="w-6 h-6 group-hover:scale-110 transition-transform" />
        </Button>
      </div>

      <PopoverBooking
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </>
  );
}

// Inline booking widget (for embedding in content)
export function InlineBookingWidget({ 
  config, 
  onBookingComplete, 
  onBookingStart,
  className 
}: BookingPluginProps) {
  const [isOpen, setIsOpen] = useState(false);
  const defaultConfig = usePluginConfig();
  const pluginConfig = { ...defaultConfig, ...config };

  return (
    <div className={cn("w-full max-w-md mx-auto", className)}>
      <div className="bg-gradient-to-r from-primary/10 to-primary-light/10 rounded-2xl p-6 border border-primary/20">
        <div className="text-center mb-4">
          <Calendar className="w-12 h-12 text-primary mx-auto mb-3" />
          <h3 className="text-xl font-bold text-foreground mb-2">
            Ready to Book?
          </h3>
          <p className="text-muted-foreground text-sm">
            Schedule your {pluginConfig.name} appointment in just a few clicks.
          </p>
        </div>
        
        <Button
          onClick={() => {
            setIsOpen(true);
            onBookingStart?.();
          }}
          className="w-full bg-gradient-primary hover:opacity-90 transition-opacity"
          size="lg"
        >
          <Calendar className="w-5 h-5 mr-2" />
          Book Now
        </Button>
      </div>

      <PopoverBooking
        isOpen={isOpen}
        onClose={() => setIsOpen(false)}
        onComplete={(data) => {
          onBookingComplete?.(data);
          setIsOpen(false);
        }}
      />
    </div>
  );
}

// Export all variants
export { BookingPlugin as default };

