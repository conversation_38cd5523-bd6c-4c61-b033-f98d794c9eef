import { Check } from "lucide-react";
import { cn } from "@/lib/utils";

interface BookingProgressProps {
  currentStep: number;
  totalSteps: number;
  steps: string[];
  onStepClick: (stepNumber: number) => void; // New prop
}

export function BookingProgress({
  currentStep,
  totalSteps,
  steps,
  onStepClick,
}: BookingProgressProps) {
  return (
    <div className="w-full py-4">
      <div className="flex items-center justify-between relative">
        {/* Progress line */}
        <div className="absolute top-6 left-0 right-0 h-0.5 bg-booking-step-inactive">
          <div
            className="h-full bg-booking-step transition-all duration-500 ease-out"
            style={{
              width: `${((currentStep - 1) / (totalSteps - 1)) * 100}%`,
            }}
          />
        </div>

        {/* Steps */}
        {steps.map((step, index) => {
          const stepNumber = index + 1;
          const isComplete = stepNumber < currentStep;
          const isCurrent = stepNumber === currentStep;
          const isInactive = stepNumber > currentStep;

          return (
            <div
              key={index}
              className="flex items-center relative z-10 cursor-pointer"
              onClick={() => onStepClick(stepNumber)}
            >
              <div
                className={cn(
                  "w-4 h-4 rounded-full transition-all duration-300",
                  {
                    "bg-booking-step shadow-lg": isCurrent,
                    "bg-booking-step-complete": isComplete,
                    "bg-booking-step-inactive": isInactive,
                  }
                )}
              />
            </div>
          );
        })}
      </div>
    </div>
  );
}
