import { useState, useEffect } from "react";
import { X, Calendar } from "lucide-react";
import { Button } from "@/components/ui/button";
import { MinimalProgressBar } from "./CleanProgressBar";
import { WelcomeStep } from "./steps/WelcomeStep";
import { ZipCodeStep } from "./steps/ZipCodeStep";
import { LocationStep } from "./steps/LocationStep";
import { ContactInfoStep } from "./steps/ContactInfoStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { usePluginConfig } from "@/config/company";
import { cn } from "@/lib/utils";
import { toast } from "@/hooks/use-toast";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  notes?: string;
}

const STEPS = [
  "Welcome",
  "Location", 
  "Contact",
  "Address",
  "Service",
  "Schedule",
  "Confirm",
];

interface PopoverBookingProps {
  isOpen: boolean;
  onClose: () => void;
  onComplete?: () => void;
}

export function PopoverBooking({ isOpen, onClose, onComplete }: PopoverBookingProps) {
  const pluginConfig = usePluginConfig();
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState(1);
  const [isComplete, setIsComplete] = useState(false);

  // Reset to first step when popover opens
  useEffect(() => {
    if (isOpen && !bookingData.currentStep) {
      setCurrentStep(1);
    } else if (isOpen && bookingData.currentStep) {
      setCurrentStep(bookingData.currentStep);
    }
  }, [isOpen, bookingData.currentStep]);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
    }

    updateBookingData({ ...dataToStore, currentStep: currentStep + 1 });
    if (currentStep < STEPS.length) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      const newStep = currentStep - 1;
      setCurrentStep(newStep);
      updateBookingData({ currentStep: newStep });
    }
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    toast({
      title: "Booking Confirmed!",
      description: "We'll be in touch shortly to confirm your appointment.",
    });
    
    // Clear data after a delay
    setTimeout(() => {
      clearBookingData();
      setCurrentStep(1);
      setIsComplete(false);
      onComplete?.();
      onClose();
    }, 3000);
  };

  const handleStartNewBooking = () => {
    clearBookingData();
    setCurrentStep(1);
    setIsComplete(false);
  };

  const handleLocationStepNext = (
    data: { street: string; city: string; state: string },
    zipCode: string
  ) => {
    goToNextStep({ ...data, zipCode });
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black/50 backdrop-blur-sm">
      <div 
        className={cn(
          "bg-white dark:bg-gray-800 rounded-2xl shadow-2xl border border-white/20 overflow-hidden",
          "w-full max-w-md max-h-[90vh] flex flex-col"
        )}
        style={{
          width: `${pluginConfig.popover?.width || 480}px`,
          maxWidth: '90vw',
        }}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b border-border">
          <div className="flex items-center gap-2">
            <Calendar className="w-5 h-5 text-primary" />
            <h2 className="font-semibold text-foreground">Book Service</h2>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-8 w-8 p-0"
          >
            <X className="w-4 h-4" />
          </Button>
        </div>

        {/* Progress Bar */}
        {!isComplete && (
          <div className="px-4 pt-2">
            <MinimalProgressBar
              currentStep={currentStep}
              totalSteps={STEPS.length}
            />
          </div>
        )}

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-4">
          {isComplete ? (
            <div className="text-center py-8">
              <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <Calendar className="w-8 h-8 text-green-600" />
              </div>
              <h3 className="text-xl font-bold text-foreground mb-2">
                Booking Confirmed!
              </h3>
              <p className="text-sm text-muted-foreground mb-4">
                Thank you for choosing our services. We'll be in touch shortly.
              </p>
              <Button
                onClick={handleStartNewBooking}
                variant="outline"
                size="sm"
              >
                Book Another
              </Button>
            </div>
          ) : (
            <>
              {/* Step 1: Welcome */}
              {currentStep === 1 && (
                <WelcomeStep onNext={() => goToNextStep({})} mode="popover" />
              )}

              {/* Step 2: Zip Code */}
              {currentStep === 2 && (
                <ZipCodeStep
                  onNext={goToNextStep}
                  initialData={{ zipCode: bookingData.zipCode || "" }}
                />
              )}

              {/* Step 3: Contact Info */}
              {currentStep === 3 && (
                <ContactInfoStep
                  onNext={goToNextStep}
                  onBack={goToPreviousStep}
                  initialData={{
                    firstName: bookingData.firstName,
                    lastName: bookingData.lastName,
                    email: bookingData.email,
                    phone: bookingData.phone,
                  }}
                />
              )}

              {/* Step 4: Location */}
              {currentStep === 4 && bookingData.zipCode && (
                <LocationStep
                  onNext={handleLocationStepNext}
                  onBack={goToPreviousStep}
                  zipCode={bookingData.zipCode}
                  initialData={{
                    houseNumber: bookingData.houseNumber || "",
                    street: bookingData.street || "",
                    city: bookingData.city || "",
                  }}
                />
              )}

              {/* Step 5: Service */}
              {currentStep === 5 && (
                <ServiceStep
                  onNext={goToNextStep}
                  onBack={goToPreviousStep}
                  initialData={{
                    service: bookingData.service || "",
                    description: bookingData.description || "",
                  }}
                />
              )}

              {/* Step 6: Schedule */}
              {currentStep === 6 && (
                <ScheduleStep
                  onNext={goToNextStep}
                  onBack={goToPreviousStep}
                  initialData={{
                    date: getDateAsObject(),
                    time: bookingData.time || "",
                  }}
                />
              )}

              {/* Step 7: Confirmation */}
              {currentStep === 7 &&
                bookingData.zipCode &&
                bookingData.service &&
                getDateAsObject() &&
                bookingData.time && (
                  <ConfirmationStep
                    data={{
                      ...bookingData,
                      date: getDateAsObject() || new Date(),
                    } as any}
                    onBack={goToPreviousStep}
                    onConfirm={handleConfirmBooking}
                    onEdit={() => {}} // Simplified for popover
                  />
                )}
            </>
          )}
        </div>
      </div>
    </div>
  );
}
