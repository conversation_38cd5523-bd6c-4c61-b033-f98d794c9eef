import { useState, useEffect } from "react";
import { BookingProgress } from "./BookingProgress";
import { ZipCodeStep } from "./steps/ZipCodeStep";
import { LocationStep } from "./steps/LocationStep";
import { ContactInfoStep } from "./steps/ContactInfoStep";
import { ServiceStep } from "./steps/ServiceStep";
import { ScheduleStep } from "./steps/ScheduleStep";
import { ContactStep } from "./steps/ContactStep";
import { ConfirmationStep } from "./steps/ConfirmationStep";
import { useBookingFormStorage } from "@/hooks/use-local-storage";
import { toast } from "@/hooks/use-toast";
import { CheckCircle, Wrench } from "lucide-react";

interface BookingData {
  zipCode?: string;
  service?: string;
  description?: string;
  date?: Date;
  time?: string;
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  houseNumber?: string;
  street?: string;
  city?: string;
  notes?: string;
}

const STEPS = [
  "Zip Code",
  "Contact Info",
  "Address",
  "Service",
  "Schedule",
  "Confirm",
];

export function BookingForm() {
  const {
    bookingData,
    updateBookingData,
    clearBookingData,
    getDateAsObject,
    setDateFromObject,
  } = useBookingFormStorage();

  const [currentStep, setCurrentStep] = useState(bookingData.currentStep || 1);
  const [isComplete, setIsComplete] = useState(false);

  const goToNextStep = (stepData: Partial<BookingData>) => {
    // Convert Date to string for localStorage
    const dataToStore = { ...stepData };
    if (dataToStore.date) {
      setDateFromObject(dataToStore.date);
      // Removed: delete dataToStore.date;
    }

    updateBookingData(dataToStore);
    if (currentStep < STEPS.length) {
      setCurrentStep((prev) => prev + 1);
    }
  };

  const handleLocationStepNext = (
    data: { street: string; city: string; state: string },
    zipCode: string
  ) => {
    goToNextStep({ ...data, zipCode });
  };

  const goToPreviousStep = () => {
    if (currentStep > 1) {
      setCurrentStep((prev) => prev - 1);
    }
  };

  const handleEditStep = (step: string) => {
    switch (step) {
      case "start":
        setCurrentStep(1); // Go back to the first step (Zip Code)
        break;
      case "service":
        setCurrentStep(4); // ServiceStep
        break;
      case "schedule":
        setCurrentStep(5); // ScheduleStep
        break;
      case "contact":
        setCurrentStep(2); // ContactInfoStep
        break;
      case "location":
        setCurrentStep(3); // LocationStep
        break;
      default:
        break;
    }
  };

  const goToStep = (step: number) => {
    setCurrentStep(step);
  };

  const handleConfirmBooking = () => {
    setIsComplete(true);
    clearBookingData(); // Clear form data after successful booking
    toast({
      title: "Booking Confirmed!",
      description:
        "Your appointment has been successfully scheduled. You'll receive a confirmation email shortly.",
    });
  };

  const handleStartNewBooking = () => {
    setCurrentStep(1);
    clearBookingData();
    setIsComplete(false);
  };

  if (isComplete) {
    return (
      <div className="min-h-screen bg-gradient-subtle flex items-center justify-center p-4">
        <div className="w-full max-w-md text-center">
          <div className="w-20 h-20 bg-booking-step-complete rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle className="w-10 h-10 text-white" />
          </div>
          <h1 className="text-3xl font-bold text-foreground mb-4">
            Booking Confirmed!
          </h1>
          <p className="text-muted-foreground mb-8">
            Thank you for choosing our services. We'll be in touch shortly to
            confirm your appointment details.
          </p>
          <button
            onClick={handleStartNewBooking}
            className="w-full bg-gradient-primary text-white py-3 px-6 rounded-lg font-medium hover:opacity-90 transition-opacity"
          >
            Book Another Appointment
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-green-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900">
      <div className="container mx-auto px-4 py-6 overflow-y-auto">
        {/* Header */}
        <div className="text-center">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-600 to-green-600 rounded-full mb-6">
            <Wrench className="w-8 h-8 text-white" />
          </div>
          <h1 className="text-3xl sm:text-4xl font-bold bg-gradient-to-r from-blue-600 to-green-600 bg-clip-text text-transparent mb-3">
            Make it a great day!
          </h1>
          <p className="text-base sm:text-md text-muted-foreground max-w-2xl mx-auto">
            Schedule your appointment in just a few easy steps. Our qualified
            technicians are ready to help with all your home service needs.
          </p>
        </div>

        {/* Progress Indicator */}
        <div className="max-w-4xl mx-auto">
          <BookingProgress
            currentStep={currentStep}
            totalSteps={STEPS.length}
            steps={STEPS}
            onStepClick={goToStep} // Pass goToStep function
          />
        </div>

        {/* Form Steps */}
        <div className="max-w-4xl mx-auto bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm rounded-2xl shadow-2xl border border-white/20 p-6">
          {/* Step 1: Zip Code */}
          {currentStep === 1 && (
            <ZipCodeStep
              onNext={goToNextStep}
              initialData={{ zipCode: bookingData.zipCode || "" }}
            />
          )}

          {/* Step 2: Contact Info */}
          {currentStep === 2 && (
            <ContactInfoStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                firstName: bookingData.firstName,
                lastName: bookingData.lastName,
                email: bookingData.email,
                phone: bookingData.phone,
              }}
            />
          )}

          {/* Step 3: Address */}
          {currentStep === 3 && (
            <LocationStep
              onNext={handleLocationStepNext}
              onBack={goToPreviousStep}
              zipCode={bookingData.zipCode || ""}
              initialData={{
                houseNumber: bookingData.houseNumber,
                street: bookingData.street,
                city: bookingData.city,
              }}
              contactInfo={{
                firstName: bookingData.firstName || "",
                lastName: bookingData.lastName || "",
                email: bookingData.email || "",
                phone: bookingData.phone || "",
              }}
              onEditContact={() => goToStep(2)}
            />
          )}

          {/* Step 4: Service */}
          {currentStep === 4 && (
            <ServiceStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                service: bookingData.service || "",
                description: bookingData.description || "",
              }}
              contactInfo={{
                firstName: bookingData.firstName || "",
                lastName: bookingData.lastName || "",
                email: bookingData.email || "",
                phone: bookingData.phone || "",
              }}
              locationInfo={{
                zipCode: bookingData.zipCode || "",
                houseNumber: bookingData.houseNumber || "",
                street: bookingData.street || "",
                city: bookingData.city || "",
              }}
              onEditContact={() => goToStep(2)}
              onEditLocation={() => goToStep(3)}
            />
          )}

          {/* Step 5: Schedule */}
          {currentStep === 5 && (
            <ScheduleStep
              onNext={goToNextStep}
              onBack={goToPreviousStep}
              initialData={{
                date: getDateAsObject(),
                time: bookingData.time || "",
              }}
            />
          )}

          {/* Step 6: Confirmation */}
          {currentStep === 6 &&
            bookingData.zipCode &&
            bookingData.service &&
            getDateAsObject() && // Use getDateAsObject() for the check
            bookingData.time && (
              <ConfirmationStep
                data={
                  {
                    ...bookingData,
                    date: getDateAsObject() || new Date(),
                  } as any
                }
                onBack={goToPreviousStep}
                onConfirm={handleConfirmBooking}
                onEdit={handleEditStep}
              />
            )}
        </div>
      </div>
    </div>
  );
}
