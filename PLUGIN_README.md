# Booking System Plugin

A modern, customizable booking system that can be embedded as a plugin on any website or used as a standalone application.

## Features

- ✨ **Clean Design**: Modern UI with smooth animations and clean progress bar (no dots)
- 🏢 **Company Branding**: Customizable logo, colors, and company information
- 📱 **Responsive**: Works perfectly on desktop, tablet, and mobile devices
- 🔌 **Plugin Ready**: Multiple integration options for easy embedding
- 🎨 **Customizable**: Easy to customize colors, text, and behavior
- 🚀 **Fast**: Optimized performance with minimal bundle size

## Integration Options

### 1. Full Page Experience
Perfect for dedicated booking pages with complete branding and features.

```tsx
import { FullPageBooking } from '@/components/booking/FullPageBooking';

function BookingPage() {
  return <FullPageBooking />;
}
```

### 2. Floating Action Button
A floating button that stays fixed on the page for always-available booking access.

```tsx
import { FloatingBookingButton } from '@/components/booking/BookingPlugin';

function App() {
  return (
    <div>
      {/* Your website content */}
      <FloatingBookingButton
        onBookingComplete={(data) => console.log('Booking completed:', data)}
        onBookingStart={() => console.log('Booking started')}
      />
    </div>
  );
}
```

### 3. Inline Widget
An inline widget that can be embedded directly in your content flow.

```tsx
import { InlineBookingWidget } from '@/components/booking/BookingPlugin';

function ServicePage() {
  return (
    <div>
      <h1>Our Services</h1>
      <p>Book your appointment today!</p>
      
      <InlineBookingWidget
        onBookingComplete={(data) => handleBooking(data)}
      />
    </div>
  );
}
```

### 4. Custom Trigger
A customizable trigger button with full control over positioning and behavior.

```tsx
import { BookingPlugin } from '@/components/booking/BookingPlugin';

function App() {
  return (
    <BookingPlugin
      config={{
        popover: {
          width: 480,
          height: 600,
          position: 'bottom-right',
          trigger: 'button',
          triggerText: 'Schedule Service',
        }
      }}
      onBookingComplete={(data) => handleBooking(data)}
    />
  );
}
```

## Configuration

### Company Branding
Customize the company information in `src/config/company.ts`:

```tsx
export const defaultCompanyConfig: CompanyConfig = {
  name: "Your Company Name",
  tagline: "Your tagline here",
  logo: "/path-to-your-logo.svg",
  colors: {
    primary: "hsl(259 94% 51%)",
    secondary: "hsl(142 71% 45%)",
    accent: "hsl(259 100% 97%)",
  },
  contact: {
    phone: "(*************",
    email: "<EMAIL>",
    website: "https://yourcompany.com",
  },
  services: [
    "Service 1",
    "Service 2",
    "Service 3"
  ]
};
```

### Plugin Options
Configure plugin behavior:

```tsx
interface PluginConfig {
  mode: 'fullpage' | 'popover';
  popover?: {
    width: number;
    height: number;
    position: 'center' | 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
    trigger: 'button' | 'auto' | 'scroll';
    triggerText?: string;
  };
  theme: 'light' | 'dark' | 'auto';
}
```

## Booking Flow

1. **Welcome Step**: Company branding and introduction
2. **Location**: Zip code entry
3. **Contact**: Customer information
4. **Address**: Detailed address information
5. **Service**: Service selection and description
6. **Schedule**: Date and time selection
7. **Confirmation**: Review and confirm booking

## Styling

The plugin uses CSS custom properties for easy theming:

```css
:root {
  --primary: 259 94% 51%;
  --booking-step: 259 94% 51%;
  --booking-step-complete: 142 71% 45%;
  --booking-step-inactive: 220 13% 91%;
}
```

## Events

### onBookingComplete
Triggered when a booking is successfully completed.

```tsx
const handleBookingComplete = (bookingData) => {
  // Handle completed booking
  console.log('Booking completed:', bookingData);
  // Send to your backend, show confirmation, etc.
};
```

### onBookingStart
Triggered when the booking process begins.

```tsx
const handleBookingStart = () => {
  // Track booking start event
  analytics.track('booking_started');
};
```

## Demo

Visit the demo pages to see the plugin in action:

- **Full Page**: `/booking` - Complete booking experience
- **Plugin Demo**: `/demo` - Interactive plugin demonstrations
- **Home**: `/` - Overview and navigation

## Browser Support

- Chrome 90+
- Firefox 88+
- Safari 14+
- Edge 90+

## License

MIT License - feel free to use in your projects!
